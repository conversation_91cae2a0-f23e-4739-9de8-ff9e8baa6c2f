locals {
  ami = "ami-08b5b3a93ed654d19"
  key_name = "lp-us-east-staging"
}

resource "aws_security_group" "confluent" {
   name        = "confluent-proxy-sg"
  description = "confluent proxy security group"
  vpc_id      = data.terraform_remote_state.staging_vpc1.outputs.vpc_id

  tags = {
    Name = "confluent sg"
  }
}

resource "aws_security_group_rule" "ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["*********/16"]
  security_group_id = aws_security_group.confluent.id
  description       = "Allow SSH inbound traffic from specified make.com IPs"
}

resource "aws_security_group_rule" "allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.confluent.id
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "postgres" {
  type              = "ingress"
  from_port         = 5432
  to_port           = 5432
  protocol          = "tcp"
  cidr_blocks       =  var.make_cidr_blocks
  security_group_id = aws_security_group.confluent.id
  description       = "Allow HTTP inbound traffic"
}


resource "aws_instance" "confluent" {
  ami                         = local.ami
  instance_type               = "t3.nano"
  subnet_id                   = data.terraform_remote_state.staging_vpc1.outputs.public_subnet_ids[0]
  associate_public_ip_address = true
  vpc_security_group_ids      = [aws_security_group.confluent.id]
  key_name                    = local.key_name

  root_block_device {
    volume_size = 15
    volume_type = "gp3"
  }

  tags = {
    Name = "confluent proxy"
  }

  lifecycle {
    ignore_changes = [vpc_security_group_ids]
  }
}

resource "aws_eip" "confluent_eip" {
  instance = aws_instance.confluent.id
  domain   = "vpc"
  tags = {
    Name = "confluent proxy"
  }
}
