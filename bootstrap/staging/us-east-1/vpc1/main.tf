module "staging_vpc1" {
  source = "**************:luxurypresence/tf-modules.git//vpc?ref=main&depth=1"
  aws_region = "us-east-1"
  vpc_name = "staging_vpc1"
  vpc_cidr = "10.21.0.0/16"
  public_subnet_cidrs = ["10.21.0.0/18", "10.21.64.0/19", "10.21.96.0/19"]
  additional_vpc_tags = {
    "peering" = "staging_vpc1"
  }
  additional_public_subnet_tags = {
    Type = "public-vpc1"
    "kubernetes.io/role/elb" = 1
    "kubernetes.io/cluster/staging_eks1" = "shared"
  }
  private_subnet_cidrs = ["10.21.128.0/18", "10.21.192.0/19", "10.21.224.0/19"]
  additional_private_subnet_tags = {
    Type = "private-vpc1"
    "kubernetes.io/role/internal-elb" = 1
    "kubernetes.io/cluster/staging_eks1" = "shared"
  }
  availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]
  owner = "swat"
  create_api_gateway_endpoint = false
  allowed_cidr_blocks = [
    "172.31.0.0/16", # Legacy VPC
    "172.16.0.0/16", # Bastion VPC
  ]
}
